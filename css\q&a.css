.question-answer {
    margin-top: 6.875rem;
}

.answer-content {
    margin-top: 4.5rem;
    width: 100%;
    height: auto;
    background: #F8F9FD;
}

.answer-item {
    font-weight: bold;
    font-size: 24px;
}

.answer-item .answer-item-top {
    border-bottom: 1px solid #E0E0E0;
}

.answer-item:last-child .answer-item-top {
    border-bottom: none;
}

/* 添加展开时的分割线样式 */
.answer-item:last-child .answer-item-top.active {
    border-bottom: 1px solid #E0E0E0;
}

.answer-item-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5rem 0;
    margin: 0 4rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-item-icon {
    cursor: pointer;
    font-size: 28px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.answer-item-content {
    padding: 2.5rem 4rem 2.5rem;
    font-size: 18px;
    font-weight: normal;
    line-height: 1.6;
    color: #666;
    background-color: #F8F9FD;
    border-bottom: 1px solid #E0E0E0;
}

.answer-item:last-child .answer-item-content {
    border-bottom: none;
}

.answer-item-top:hover {
    background-color: transparent;
}

.answer-item-title {
    color: #000;
    transition: none;
}

.answer-item-top:hover .answer-item-title {
    color: #000;
}

