// major-function动画处理
function initMajorFunctionAnimation() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 处理major-function-content动画
                if (entry.target.classList.contains('major-function-content')) {
                    entry.target.classList.add('animate');
                    const items = entry.target.querySelectorAll('.major-function-content-right-item');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 200 * index);
                    });
                }
                
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });

    // 监听major-function-content元素
    const elements = document.querySelectorAll('.major-function-content');
    elements.forEach(element => {
        observer.observe(element);
    });
}

// 当DOM加载完成后初始化动画
document.addEventListener('DOMContentLoaded', function() {
    initMajorFunctionAnimation();
}); 