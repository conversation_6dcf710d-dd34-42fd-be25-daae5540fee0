document.addEventListener('DOMContentLoaded', function() {
    // 检测元素是否在视口中
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // 添加动画类
    function addAnimationClass(element, animationClass) {
        if (isElementInViewport(element) && !element.classList.contains(animationClass)) {
            element.classList.add(animationClass);
        }
    }

    // 需要添加动画的元素
    const animatedElements = [
        {
            element: document.querySelector('.staff-management-banner'),
            animationClass: 'fade-in-up'
        },
        {
            element: document.querySelector('.staff-management-data'),
            animationClass: 'fade-in'
        },
        {
            element: document.querySelector('.management-scheme .base-title'),
            animationClass: 'fade-in-up'
        },
        {
            element: document.querySelector('.scheme-content .content-item:nth-child(1)'),
            animationClass: 'slide-in-left'
        },
        {
            element: document.querySelector('.scheme-content .content-item:nth-child(2)'),
            animationClass: 'slide-in-right'
        },
        {
            element: document.querySelector('.scheme-content .content-item:nth-child(3)'),
            animationClass: 'slide-in-left'
        },
        {
            element: document.querySelector('.major-function'),
            animationClass: 'fade-in-up'
        }
    ];

    // 添加CSS动画样式
    const styleSheet = document.createElement('style');
    styleSheet.innerHTML = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .fade-in {
            animation: fadeIn 1s ease forwards;
            opacity: 0;
        }
        
        .fade-in-up {
            animation: fadeInUp 1s ease forwards;
            opacity: 0;
        }
        
        .slide-in-left {
            animation: slideInLeft 1s ease forwards;
            opacity: 0;
        }
        
        .slide-in-right {
            animation: slideInRight 1s ease forwards;
            opacity: 0;
        }
    `;
    document.head.appendChild(styleSheet);

    // 初始设置所有元素为不可见
    animatedElements.forEach(item => {
        if (item.element) {
            item.element.style.opacity = '0';
        }
    });

    // 滚动时检查元素并添加动画
    function checkElementsInViewport() {
        animatedElements.forEach(item => {
            if (item.element) {
                addAnimationClass(item.element, item.animationClass);
            }
        });
    }

    // 数据项的数字增长动画
    function animateNumbers() {
        const dataItems = document.querySelectorAll('.data-item-num');
        dataItems.forEach(item => {
            const finalValue = parseInt(item.textContent);
            let startValue = 0;
            const duration = 2000; // 动画持续时间（毫秒）
            const frameRate = 50; // 每秒帧数
            const totalFrames = duration / 1000 * frameRate;
            const increment = finalValue / totalFrames;
            
            if (isElementInViewport(item.parentElement.parentElement) && !item.dataset.animated) {
                item.dataset.animated = 'true';
                let currentValue = 0;
                const counter = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        clearInterval(counter);
                        item.textContent = finalValue;
                    } else {
                        item.textContent = Math.floor(currentValue);
                    }
                }, 1000 / frameRate);
            }
        });
    }

    // 初始运行一次检查
    checkElementsInViewport();
    animateNumbers();

    // 监听滚动事件
    window.addEventListener('scroll', function() {
        checkElementsInViewport();
        animateNumbers();
    });
}); 