.service-marketing-main {
    margin-top: 10rem;
}

.service-marketing-banner {
    width: 100%;
    height: auto;
    padding-bottom: 4rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-desc {
    font-size: 18px;
    margin-top: 2.125rem;
}

.banner-left .banner-title {
    font-size: 56px;
    font-weight: bold;
}

.banner-title-first {
    color: #10B981;
}

.banner-right {
    width: 36rem;
    height: 27.625rem;
}

.banner-right img {
    width: 100%;
    height: 100%;
}

.service-marketing-workbenches {
    padding-top: 6.25rem;
    padding-bottom: 5rem;
    background: #F8F9FD;
}

.workbenches-bg {
    margin-top: 4.625rem;
    width: 100%;
    height: 39.875rem;
}

.service-marketing-important {
    margin-top: 6.875rem;
}

.important-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}


.important-list {
    margin-top: 4.375rem;
}

.important-item {
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 32%;
    height: 24.5rem;
    background: #F8F9FD;
    border-radius: 0.5rem;
}

.important-item:nth-child(n+4) {
    margin-top: 1.5rem;
}

.important-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.375rem;
    height: 3.375rem;
    background: #E0FFF5;
    border-radius: 50%;
}

.important-item-icon img {
    width: 1.75rem;
    height: 1.3125rem;
}

.important-item-title {
    margin-top: 1.625rem;
    margin-bottom: 2rem;
    font-weight: bold;
    font-size: 20px;
}

.important-item-desc {
    text-align: center;
}

.service-marketing-scheme {
    margin-top: 6.875rem;
    padding: 6.875rem 0;
    width: 100%;
    background: #EDF2F7;
}

.marketing-scheme-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 5rem;
    width: 100%;
    height: 33.125rem;
    border-radius: 1.5rem;
    overflow: hidden;
}

.scheme-item-img,
.scheme-item-introduce {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50%;
    height: 100%;
}

.scheme-item-introduce {
    background: #fff;
}


.bg-marketing-1 {
    background: linear-gradient(45deg, #CDE7E5 0%, #DBEEED 23%, #CDE7E5 100%);
}

.bg-marketing-2 {
    background: linear-gradient(-45deg, #E4DFE9 0%, #EEEAF1 41%, #E4DEE9 100%);
}

.bg-marketing-3 {
    background: linear-gradient(45deg, #D5DAEB 0%, #E7E9F3 23%, #D9DEED 100%);

}

.bg-marketing-4 {
    background: linear-gradient(-45deg, #E9DFDE 0%, #F3EDEC 41%, #ECE2E2 100%);
}

.scheme-item-img-1 {
    width: 32.375rem;
    height: 18.625rem;
}

.scheme-item-img-2 {
    width: 32.375rem;
    height: 25.3125rem;
}

.scheme-item-img-3 {
    width: 34.125rem;
    height: 21.5rem;
}

.scheme-item-img-4 {
    width: 32.875rem;
    height: 21.75rem;
}

/* 动画效果 */
.service-marketing-banner .banner-content {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.service-marketing-banner .banner-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.service-marketing-workbenches .workbenches-bg {
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.service-marketing-workbenches .workbenches-bg.animate {
    opacity: 1;
    transform: scale(1);
}

.service-marketing-important {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.service-marketing-important.animate {
    opacity: 1;
    transform: translateY(0);
}

.important-list .important-item {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.important-list .important-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.marketing-scheme-item {
    opacity: 0;
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.marketing-scheme-item:nth-child(odd) {
    transform: translateX(-50px);
}

.marketing-scheme-item:nth-child(even) {
    transform: translateX(50px);
}

.marketing-scheme-item.animate {
    opacity: 1;
    transform: translateX(0);
}

.major-function-content {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.major-function-content.animate {
    opacity: 1;
    transform: translateY(0);
}
