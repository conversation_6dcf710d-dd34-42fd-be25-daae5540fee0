$(document).ready(function () {
    const featuresMenu = $('.features');
    const subHeader = $('.sub-header');

    if (featuresMenu.length && subHeader.length) {
        featuresMenu.on('mouseenter', function () {
            subHeader.css('display', 'block');
        });

        featuresMenu.on('mouseleave', function (e) {
            // 检查鼠标是否移动到子菜单
            const rect = subHeader[0].getBoundingClientRect();
            if (
                e.clientX < rect.left ||
                e.clientX > rect.right ||
                e.clientY < rect.top ||
                e.clientY > rect.bottom
            ) {
                subHeader.css('display', 'none');
            }
        });

        subHeader.on('mouseenter', function () {
            subHeader.css('display', 'block');
        });

        subHeader.on('mouseleave', function () {
            subHeader.css('display', 'none');
        });
    }
});
