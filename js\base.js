$(document).ready(function () {
    // 获取当前路径
    const isRoot = location.pathname === '/' || location.pathname.endsWith('index.html');
    const imgPath = isRoot ? './assets/images/common/arrow-top.png' : '../assets/images/common/arrow-top.png';
    const tgPath = isRoot ? './assets/images/common/tg.png' : '../assets/images/common/tg.png';
    const lightningPath = isRoot ? './assets/images/common/lightning.png' : '../assets/images/common/lightning.png';

    // 创建返回顶部按钮
    const backToTop = $('<div class="back-to-top"><img src="' + imgPath + '" alt="imgPath"></div>');
    $('body').append(backToTop);

    // 创建Telegram按钮
    const telegramBtn = $('<a href="https://t.me/a2c_chat" target="_blank"> <div class="telegram-btn"><img src="' + tgPath + '" alt="tgPath">Telegram</div></a>');
    $('body').append(telegramBtn);

    // 创建powered by文字
    const telegramPowered = $('<div class="telegram-powered"><img src="' + lightningPath + '" alt="lightningPath"><span class="powered-by">Powered by</span> <span class="powered-by-a2c">A2C</span></div>');
    $('body').append(telegramPowered);

    // 检测powered-by元素是否与api-detail-information元素重叠
    function checkOverlap() {
        const $apiDetail = $('.api-detail-information');
        const $poweredBy = $('.powered-by');
        
        if ($apiDetail.length && $poweredBy.length) {
            const apiRect = $apiDetail[0].getBoundingClientRect();
            const poweredRect = $poweredBy[0].getBoundingClientRect();
            
            // 检查两个元素是否重叠
            if (!(apiRect.right < poweredRect.left || 
                  apiRect.left > poweredRect.right || 
                  apiRect.bottom < poweredRect.top || 
                  apiRect.top > poweredRect.bottom)) {
                $poweredBy.css('color', 'white');
            } else {
                $poweredBy.css('color', '');
            }
        }
    }

    // 定期检查元素位置
    setInterval(checkOverlap, 200);

    // 滚动时也检查
    $(window).on('scroll', checkOverlap);

    // 立即检查当前滚动位置
    checkScrollPosition();

    // 监听滚动事件
    $(window).scroll(function () {
        checkScrollPosition();
    });

    // 检查滚动位置函数
    function checkScrollPosition() {
        if ($(window).scrollTop() > 300) {
            backToTop.addClass('show');
        } else {
            backToTop.removeClass('show');
        }
    }

    // 点击返回顶部
    backToTop.click(function () {
        $('html, body').animate({
            scrollTop: 0
        }, 500);
        return false;
    });
});
