.w {
    max-width: 100%;
    overflow: visible;
}

.mass-sending-main {
    margin-top: 10rem;
    max-width: 100%;
}

.mass-sending-banner {
    width: 100%;
    height: auto;
    padding-bottom: 4rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
    overflow: visible;
    position: relative;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
    overflow: visible;
}

.banner-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-left .banner-title {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.8s ease-out;
}

.banner-left .banner-title.animate {
    opacity: 1;
    transform: translateX(0);
}

.banner-left .banner-title:nth-child(2) {
    transition-delay: 0.2s;
}

.banner-desc {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.8s ease-out 0.4s;
}

.banner-desc.animate {
    opacity: 1;
    transform: translateY(0);
}

.banner-desc {
    width: 71%;
    font-size: 18px;
    margin-top: 2rem;
}

.banner-left .banner-title {
    font-size: 56px;
    font-weight: bold;
}

.banner-right {
    width: 23.625rem;
    height: 33.25rem;
    opacity: 0;
    transform: translateX(30px) scale(0.9);
    transition: all 1s ease-out 0.6s;
    overflow: visible;
    border-radius: 10px;
    /* 为浮动动画预留空间 */
    margin: 30px 0;
}

.banner-right.animate {
    opacity: 1;
    transform: translateX(0) scale(1);
}

.banner-right img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.banner-right:hover img {
    transform: scale(1.02);
}

.banner-title-first {
    color: #10B981;
}

.mass-sending-scene {
    padding: 6.875rem 0;
    overflow: hidden;
}

.scene-content {
    margin-top: 4.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.scene-content-item {
    width: 49%;
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.scene-content-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.scene-content-item:nth-child(2) {
    transition-delay: 0.2s;
}

.scene-content-item-high {
    padding-top: 1.5rem;
    padding-left: 1.5rem;
    width: 100%;
    height: 33.75rem;
    border-radius: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.scene-content-item-high:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.scene-content-item-low {
    padding-top: 1.5rem;
    padding-left: 1.5rem;
    display: flex;
    align-items: center;
    width: 100%;
    height: 18.75rem;
    border-radius: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.scene-content-item-low:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.scene-content-item-bg1 {
    background: #DFF8F4;
}

.scene-content-item-bg2 {
    margin-top: 1.25rem;
    background: #FDF9E9;
}

.scene-content-item-bg3 {
    background: #DAE6FF;
}

.scene-content-item-bg4 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-top: 1.25rem;
    background: #F9EDFF;
}

.scene-content-item-icon {
    width: 2rem;
    height: 2rem;
    transition: transform 0.3s ease;
}

.scene-content-item-icon img {
    width: 100%;
    height: 100%;
}

.scene-content-item:hover .scene-content-item-icon {
    transform: scale(1.1) rotate(5deg);
}

.scene-content-item-title {
    font-size: 24px;
    font-weight: bold;
    margin-top: 1.5rem;
}

.scene-content-item-desc {
    margin-top: 1.5rem;
    font-size: 18px;
}

.scene-low-left {
    width: 50%;
    height: 100%;
}

.scene-content-item-img1 {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 3.25rem;
    width: 100%;
    height: 19.125rem;
}

.scene-content-item-img1 img {
    width: 20.625rem;
    height: 100%;
}

.scene-content-item-img2 {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.scene-content-item-img2 img {
    width: 20.625rem;
    height: 15rem;

}

.scene-content-item-img3 {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.scene-content-item-img3 img {
    width: 20.375rem;
    height: 16.25rem;

}

.scene-content-item-img4 {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
}

.scene-content-item-img4 img {
    width: 28rem;
    height: 22.25rem;

}

.content-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.content-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.advantage-bg-color {
    background: linear-gradient(90deg, #E4EBFC 0%, #E9F6F5 40%, #FAF8E4 100%);
}

.advantage-1 {
    width: 18.4375rem;
    height: 20.25rem;
}

.advantage-2 {
    width: 18.375rem;
    height: 20.25rem;
}

.core-functions {
    padding: 6.875rem 0;
    background: #E5E7F2;
    overflow: hidden;
}

.core-functions-content {
    margin-top: 6.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.core-functions-content-left {
    width: 22.125rem;
    height: 40.75rem;
    margin-right: 6.875rem;
}

.core-functions-content-left img {
    width: 100%;
    height: 100%;
}

.core-functions-content-right-item {
    background: #fff;
    padding: 2.5rem 3.125rem;
    width: 39rem;
    height: 13.875rem;
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.6s ease-out;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    cursor: pointer;
}

.core-functions-content-right-item.animate {
    opacity: 1;
    transform: translateX(0);
}

.core-functions-content-right-item:nth-child(2) {
    transition-delay: 0.2s;
}

.core-functions-content-right-item:nth-child(3) {
    transition-delay: 0.4s;
}

.core-functions-content-right-item:hover {
    transform: translateX(10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.core-functions-content-right-item-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 1.5rem;
}

.core-functions-content-right-item:nth-child(-n+2) {
    margin-bottom: 1.25rem;
}

.mass-sending-advantage {
    width: 100%;
    background: #F8F9FD;
    padding: 6.875rem 0;
    overflow: hidden;
}

.mass-sending-advantage-content {
    margin-top: 4.375rem;
    width: 100%;
}

.mass-sending-advantage-content-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-top-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: #fff;
    padding: 6.25rem 2rem;
    width: 32%;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s ease-out;
    cursor: pointer;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.content-top-item.animate {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.content-top-item:nth-child(2) {
    transition-delay: 0.2s;
}

.content-top-item:nth-child(3) {
    transition-delay: 0.4s;
}

.content-top-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.mass-content-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.375rem;
    height: 3.375rem;
    background: #E0FFF5;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mass-content-item-icon img {
    width: 1.875rem;
    height: 1.875rem;
    transition: transform 0.3s ease;
}

.content-top-item:hover .mass-content-item-icon {
    background: #10B981;
    transform: scale(1.1);
}

.content-top-item:hover .mass-content-item-icon img {
    transform: rotate(360deg);
}

.mass-content-item-title {
    font-size: 20px;
    font-weight: bold;
    margin-top: 1.7rem;
}

.mass-content-item-desc {
    margin-top: 2rem;
    text-align: center;
}


.mass-sending-advantage-content-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
}


.content-bottom-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: #fff;
    padding: 6.25rem 2rem;
    width: 49%;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: all 0.6s ease-out;
    cursor: pointer;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.content-bottom-item.animate {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.content-bottom-item:nth-child(2) {
    transition-delay: 0.2s;
}

.content-bottom-item:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.content-bottom-item:hover .mass-content-item-icon {
    background: #10B981;
    transform: scale(1.1);
}

.content-bottom-item:hover .mass-content-item-icon img {
    transform: rotate(360deg);
}

.major-function-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.major-function-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.major-function-content-right-item {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease-out;
}

.major-function-content-right-item.animate {
    opacity: 1;
    transform: translateX(0);
}

/* 数字计数动画 */
.number-counter {
    display: inline-block;
    font-weight: bold;
    color: #10B981;
}

/* 渐变背景动画 */
.gradient-bg {
    background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 脉冲动画 */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 浮动动画 */
.float-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
    100% {
        transform: translateY(0px);
    }
}

/* 旋转动画 */
.rotate-animation {
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 闪烁动画 */
.blink-animation {
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.7;
    }
}

/* 弹跳动画 */
.bounce-animation {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* 标题文字动画 */
.banner-title-first {
    position: relative;
    overflow: hidden;
}

.banner-title-first::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 卡片翻转效果 */
.flip-card {
    perspective: 1000px;
    width: 100%;
    height: 100%;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 10px;
}

.flip-card-back {
    transform: rotateY(180deg);
    background: linear-gradient(135deg, #10B981, #059669);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

/* 进度条动画 */
.progress-bar {
    width: 100%;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10B981, #059669);
    border-radius: 2px;
    transform: translateX(-100%);
    animation: fillProgress 2s ease-out forwards;
}

@keyframes fillProgress {
    to {
        transform: translateX(0);
    }
}

/* 粒子效果背景 */
.particles-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: #10B981;
    border-radius: 50%;
    opacity: 0.6;
    animation: particleFloat 15s infinite linear;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* 标题装饰动画已移除 */

/* 响应式动画调整 */
@media (max-width: 768px) {
    .banner-content {
        flex-direction: column;
        text-align: center;
    }

    .banner-right {
        margin-top: 2rem;
        width: 100%;
        max-width: 300px;
        /* 确保移动端也保持overflow: visible */
        overflow: visible;
        margin: 30px auto 2rem auto;
    }

    .scene-content {
        flex-direction: column;
    }

    .scene-content-item {
        width: 100%;
        margin-bottom: 2rem;
    }

    .mass-sending-advantage-content-top {
        flex-direction: column;
    }

    .content-top-item {
        width: 100%;
        margin-bottom: 1rem;
    }

    .mass-sending-advantage-content-bottom {
        flex-direction: column;
    }

    .content-bottom-item {
        width: 100%;
        margin-bottom: 1rem;
    }
}