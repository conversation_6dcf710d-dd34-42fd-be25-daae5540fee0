// 使用Intersection Observer监听元素
document.addEventListener('DOMContentLoaded', function() {
    // 团队成员列表动画
    const teamList = document.querySelector('.team-list');
    const teamItems = document.querySelectorAll('.team-item');
    
    // 为所有team-item添加初始隐藏类
    teamItems.forEach(item => {
        item.classList.add('team-item-hidden');
    });
    
    // 创建观察者实例 - 团队成员列表
    const teamObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // 当team-list进入视口
            if (entry.isIntersecting) {
                // 依次为每个team-item添加显示动画
                teamItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('team-item-show');
                    }, 100 * index); // 每个item延迟100ms显示，形成序列动画
                });
                // 一旦动画开始，取消观察
                teamObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1 // 当10%的元素可见时触发
    });
    
    // 关于我们部分动画
    const aboutUs = document.querySelector('.about-us-detail');
    
    // 添加初始隐藏类
    if (aboutUs) {
        aboutUs.classList.add('about-us-hidden');
    }
    
    // 创建观察者实例 - 关于我们部分
    const aboutUsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // 当about-us-detail进入视口
            if (entry.isIntersecting) {
                // 添加显示动画
                entry.target.classList.add('about-us-show');
                // 一旦动画开始，取消观察
                aboutUsObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1 // 当10%的元素可见时触发
    });
    
    // 关于团队部分动画
    const aboutTeam = document.querySelector('.info-about-team');
    
    // 添加初始隐藏类
    if (aboutTeam) {
        aboutTeam.classList.add('info-about-team-hidden');
    }
    
    // 创建观察者实例 - 关于团队部分
    const aboutTeamObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // 当info-about-team进入视口
            if (entry.isIntersecting) {
                // 添加显示动画
                entry.target.classList.add('info-about-team-show');
                // 一旦动画开始，取消观察
                aboutTeamObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2 // 当20%的元素可见时触发
    });
    
    // 数据统计框动画
    const infoBox = document.querySelector('.info-box');
    
    // 添加初始隐藏类
    if (infoBox) {
        infoBox.classList.add('info-box-hidden');
    }
    
    // 创建观察者实例 - 数据统计框
    const infoBoxObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            // 当info-box进入视口
            if (entry.isIntersecting) {
                // 添加显示动画
                entry.target.classList.add('info-box-show');
                // 一旦动画开始，取消观察
                infoBoxObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.15 // 当15%的元素可见时触发
    });
    
    // 开始观察
    if (teamList) {
        teamObserver.observe(teamList);
    }
    
    if (aboutUs) {
        aboutUsObserver.observe(aboutUs);
    }
    
    if (aboutTeam) {
        aboutTeamObserver.observe(aboutTeam);
    }
    
    if (infoBox) {
        infoBoxObserver.observe(infoBox);
    }
});