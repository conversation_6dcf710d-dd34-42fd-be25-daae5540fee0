.number-maintenance-main {
    margin-top: 10rem;
}

.number-maintenance-banner {
    width: 100%;
    height: auto;
    padding-bottom: 4rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.banner-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-desc {
    width: 90%;
    font-size: 18px;
    margin-top: 2rem;
}

.banner-left .banner-title {
    font-size: 56px;
    font-weight: bold;
}

.banner-title-first {
    color: #10B981;
}

.banner-right {
    width: 34.125rem;
    height: 31rem;
}

.banner-right img {
    width: 100%;
    height: 100%;
}

.number-maintenance-process {
    margin-top: 6.25rem;
}

.process-content {
    margin-top: 4.5rem;
    margin-bottom: 6.875rem;
    display: flex;
    justify-content: space-around;
    opacity: 0;
    transition: opacity 0.8s ease-out;
}

.process-content.animate {
    opacity: 1;
}

.process-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease-out;
}

.process-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.process-arrow {
    margin-top: 1.3rem;
    width: 5rem;
    height: 1rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease-out;
}

.process-item.animate, .process-arrow.animate {
    opacity: 1;
    transform: translateY(0);
}

.process-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.5rem;
    height: 3.5rem;
    font-weight: bold;
    font-size: 22px;
    color: #008069;
    background: #EAF9F4;
    border-radius: 50%;;
}

.process-item-txt {
    margin-top: 0.875rem;
    font-size: 20px;
    font-weight: bold;
}

.process-item-desc {
    margin-top: 0.875rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.content-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.content-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.core-functions {
    padding: 6.875rem 0;
    background: #E5E7F2;
}

.core-functions-list {
    margin-top: 4.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.core-functions-list.animate {
    opacity: 1;
    transform: translateY(0);
}

.core-functions-item {
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    width: 48%;
    height: 13.125rem;
    background: #fff;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease-out;
}

.core-functions-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.core-functions-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 6.25rem;
    height: 6.25rem;
    border-radius: 50%;
    margin-right: 2rem;
}

.core-functions-item-icon img {
    width: 3.75rem;
    height: 3.75rem;
}

.core-functions-item-txt {
    flex: 1;
}

.core-functions-item-title {
    font-size: 20px;
    font-weight: bold;
}

.core-functions-item-desc {
    margin-top: 1.5rem;
    width: 100%;
}
.advantage-bg-color{
    background: linear-gradient(90deg, #E4EBFC 0%, #E9F6F5 40%, #FAF8E4 100%);
}
.advantage-1 {
    width: 22.125rem;
    height: 15rem;
}

.advantage-2 {
    width: 18.375rem;
    height: 20.1875rem;
}

.advantage-3 {
    width: 18.4375rem;
    height: 20.25rem;
}

.core-functions-item:nth-child(1) .core-functions-item-icon {
    background: #EAF9F4;
}

.core-functions-item:nth-child(2) .core-functions-item-icon {
    background: #FFEADE;
}

.core-functions-item:nth-child(3) .core-functions-item-icon {
    background: #EDDDFF;
}

.core-functions-item:nth-child(4) .core-functions-item-icon {
    background: #E1E4FF;
}

.core-functions-item:nth-child(5) .core-functions-item-icon {
    background: #FFF4DE;
}

.core-functions-item:nth-child(6) .core-functions-item-icon {
    background: #FFDEE3;
}

.core-functions-item:nth-child(-n+4) {
    margin-bottom: 1.5rem;
}

.major-function-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.major-function-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.major-function-content-right-item {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease-out;
}

.major-function-content-right-item.animate {
    opacity: 1;
    transform: translateX(0);
}