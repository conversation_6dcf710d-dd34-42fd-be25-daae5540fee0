$(document).ready(function() {
    // 默认显示第一个内容项
    $('.content-right-item').hide().eq(0).show();
    
    // 点击左侧菜单项时的事件处理
    $('.content-left-item').click(function() {
        // 获取当前点击项的索引
        const index = $(this).index();
        
        // 切换左侧菜单项的active类
        $('.content-left-item').removeClass('active');
        $(this).addClass('active');
        
        // 切换右侧内容项
        $('.content-right-item').hide().eq(index).fadeIn(300);
    });

    // 使用IntersectionObserver监听元素
    const observeElements = () => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    // 处理construct-list动画
                    if (entry.target.classList.contains('construct-list')) {
                        const items = entry.target.querySelectorAll('.construct-item');
                        items.forEach((item, index) => {
                            setTimeout(() => {
                                item.classList.add('animate');
                            }, 200 * index);
                        });
                    }
                    
                    // 处理information-content动画
                    if (entry.target.classList.contains('information-content')) {
                        entry.target.classList.add('animate');
                    }
                    
                    // 处理content-item动画 - 直接为每个item添加动画
                    if (entry.target.classList.contains('content-item')) {
                        setTimeout(() => {
                            entry.target.classList.add('animate');
                        }, 100);
                    }
                    
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.2
        });

        // 监听需要动画的元素
        const elementsToObserve = [
            '.construct-list',
            '.information-content'
        ];
        
        elementsToObserve.forEach(selector => {
            const element = document.querySelector(selector);
            if (element) {
                observer.observe(element);
            }
        });
        
        // 单独监听每个content-item
        document.querySelectorAll('.content-item').forEach(item => {
            observer.observe(item);
        });
    };

    // 调用观察函数
    observeElements();
});
