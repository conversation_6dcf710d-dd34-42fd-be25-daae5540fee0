$(document).ready(function () {
    // 获取标签和内容元素
    const $controlTab = $('.control-tab');
    const $apiTab = $('.api-tab');
    const $controlList = $('.content-control-list');
    const $apiList = $('.content-api-list');

    // 设置初始状态
    $controlList.css('display', 'flex');
    $apiList.css('display', 'none');

    // 为control-tab添加点击事件
    $controlTab.click(function () {
        $controlTab.addClass('active');
        $apiTab.removeClass('api-active');
        $controlList.css('display', 'flex');
        $apiList.css('display', 'none');
    });

    // 为api-tab添加点击事件
    $apiTab.click(function () {
        $apiTab.addClass('api-active');
        $controlTab.removeClass('active');
        $apiList.css('display', 'flex');
        $controlList.css('display', 'none');
    });
});