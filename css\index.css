.index-main {
    margin-top: 7rem;
}

.index-banner {
    margin: 0 auto;
    width: 95%;
    height: 38.75rem;
    background: url("../assets/images/index/index-banner-left.png") no-repeat left top, url("../assets/images/index/index-banner-right.png") no-repeat right bottom, linear-gradient(90deg, #D0E0FF 0%, #E8FFF3 100%) no-repeat;
    background-size: 30.5rem 24.25rem, 24.125rem 21.625rem, 100% 100%;
    border-radius: 1.25rem;
}

.index-banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
}


.certification {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 14.125rem;
    height: 2.5rem;
    background: #FFFFFF;
    border-radius: 1.25rem;
}

.certification img {
    width: 1rem;
    height: 1rem;
    margin-right: 0.875rem;
}

.index-banner-left-title {
    font-weight: bold;
    font-size: 64px;
    line-height: 1.5;
    white-space: nowrap;
}

.banner-left-title-a {
    color: #00A8FF;
}

.banner-left-title-b {
    color: #FF4757;
}

.banner-left-title-c {
    color: #2ED573;
}

.free-trial {
    margin-top: 1.875rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 9.5625rem;
    height: 3.3125rem;
    background: #161C2D;
    border-radius: 0.625rem;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
}

.free-trial a {
    color: #fff;
}

.index-banner-left-desc {
    margin-top: 2.125rem;
    width: 33.25rem;
    color: #151C2D;
}

.index-banner-right {
    position: relative;
    width: 811px;
    height: 912px;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: scale(0.5);
}

.banner-right-img {
    position: absolute;
    top: 0;
    left: -30%;
    width: 100%;
    height: 100%;
    background: url("../assets/images/index/people-number.png") no-repeat 8% 10%, url("../assets/images/index/whatsApp.png") no-repeat left 90%;
    background-size: 406px 522px, 452px 156px;
}

.sprite-container {
    width: 811px;
    height: 912px;
    background: url("../assets/images/index/sequence_sprites.png") no-repeat 0 0;
    overflow: hidden;
    background-size: 18653px 912px;
}

.index-content {
    margin-top: 3.125rem;
    width: 100%;
    height: 12.5rem;
    background: #1C7B6F;
}

.content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    height: 100%;
}

.index-content-left {
    margin-right: 3.625rem;
    width: 9.875rem;
    height: 5.625rem;
}

.index-content-left img {
    width: 100%;
    height: 100%;
}

.right-top {
    font-size: 32px;
}

.right-bottom .highlight {
    color: #FFD301
}

.index-choose {
    padding: 7.5rem 0;
    background: #F8F9FD;
}

.index-choose .choose-top {
    width: 100%;
}

.index-choose .expand-title {
    width: 100%;
    text-align: center;
    font-size: 36px;
}

.index-choose .sub-title {
    margin-top: 2rem;
    text-align: center;
}

.index-choose .logos {
    margin-top: 5.875rem;
    width: 100%;
    height: 3rem;
}

.index-choose .choose-bottom {
    margin-top: 10.625rem;
    text-align: center;
}

.service-desc {
    margin-top: 2rem;
    color: #596C7D;
}

.service-list {
    margin-top: 5rem;
    display: flex;
    align-items: start;
    justify-content: space-between;
    opacity: 0;
    transform: translateY(1.875rem);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.service-list.animate {
    opacity: 1;
    transform: translateY(0);
}

.service-item {
    width: 23%;
}

.data-num {
    font-weight: bold;
    font-size: 60px;
}

.data-noun {
    font-weight: bold;
    font-size: 22px;
    color: #10B981;
}

.service-item-desc {
    text-align: left;
    font-size: 14px;
    margin-top: 2rem;
}

.index-information {
    margin-top: 7rem;
}

.index-information .information-tab {
    margin-top: 3rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.information-tab .tab-item {
    width: 19%;
    height: 3.5625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    background: #F6F8FC;
    cursor: pointer;
}

/* 添加active状态样式 */
.information-tab .tab-item.active {
    background: #000;
    color: #fff;
}

.item-icon {
    margin-right: 0.5rem;
    width: 1.375rem;
    height: 1.375rem;
}

.txt-icon {
    background: url("../assets/images/index/txt.png") no-repeat;
    background-size: 100% 100%;
}

.txt-active {
    background: url("../assets/images/index/txt-active.png") no-repeat;
    background-size: 100% 100%;
}

.video-icon {
    background: url("../assets/images/index/video.png") no-repeat;
    background-size: 100% 100%;
}

.video-active {
    background: url("../assets/images/index/video-active.png") no-repeat;
    background-size: 100% 100%;
}

.picture-icon {
    background: url("../assets/images/index/picture.png") no-repeat;
    background-size: 100% 100%;
}

.picture-active {
    background: url("../assets/images/index/picture-active.png") no-repeat;
    background-size: 100% 100%;
}

.interactive-icon {
    background: url("../assets/images/index/interactive.png") no-repeat;
    background-size: 100% 100%;
}

.interactive-active {
    background: url("../assets/images/index/interactive-active.png") no-repeat;
    background-size: 100% 100%;
}

.file-icon {
    background: url("../assets/images/index/file.png") no-repeat;
    background-size: 100% 100%;
}

.file-active {
    background: url("../assets/images/index/file-active.png") no-repeat;
    background-size: 100% 100%;
}

.information-content {
    position: relative;
    box-sizing: border-box;
    margin-top: 1.5rem;
    width: 100%;
    height: 36.25rem;
    background: url("../assets/images/index/information-bg.png") no-repeat;
    background-size: 100% 100%;
}

.information-content-item {
    position: absolute;
    left: 6%;
    bottom: 0;
    display: flex;
}

.item-img {
    width: 18.75rem;
    height: 31.125rem;
}

.item-img img {
    width: 100%;
    height: 100%;
}

.item-desc {
    margin-left: 5rem;
    color: #fff;
    width: 40rem;
    overflow: hidden;
}

.item-desc .desc-title {
    font-size: 36px;
    margin-top: 5rem;
    margin-bottom: 1.375rem;
}
.desc-sub-title{
    margin-bottom: 1.375rem;
}
.desc-li{
    margin-top: 1rem;
}
.information-content-item:nth-child(n+2) {
    display: none;
}

.index-ability {
    margin-top: 7rem;
    padding: 7rem 0;
    background: #F3F8FE;
}

.index-ability .ability-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 0;
    transform: translateY(1.875rem);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.index-ability .ability-list.animate {
    opacity: 1;
    transform: translateY(0);
}

.ability-top-item,
.ability-bottom-item {
    background: #fff;
}

.ability-top-item {
    margin-top: 3rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 49%;
    height: 19rem;
    border-radius: 1rem;
    border: 1px solid #D2E6F9;
}

.ability-top-right {
    margin-left: 1rem;
}

.top-right-title {
    margin-bottom: 1rem;
    font-size: 22px;
    font-weight: bold;
}

.ability-top-img {
    background: #f5f9ff;
    width: 17.625rem;
    height: 16.875rem;
}

.ability-bottom-item {
    margin-top: 1.5rem;
    padding: 1.5rem;
    width: 32.5%;
    height: 12.625rem;
    border-radius: 1rem;
    border: 1px solid #D2E6F9;
}

.ability-item-icon {
    width: 2.5rem;
    height: 2.5rem;
}

.ability-item-introduce {
    font-weight: bold;
    margin: 1.75rem 0 1rem;
}

.index-use {
    margin-top: 7rem;
}

.use-tab-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4.375rem;
}

.use-tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    width: 13%;
    height: 3.25rem;
    border-radius: 1.625rem;
    border: 2px solid #161C2D;
    cursor: pointer;
}

.use-tab-item.use-active {
    background: #161C2D;
    color: #fff;
}

.use-content {
    margin-top: 5rem;
}

.use-content-item {
    display: flex;
    align-items: center;
}

.use-item-left {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35.5rem;
    height: 35.5rem;
    background: #EBF1F7;
    border-radius: 2rem;
}

.use-content-item:nth-child(n+2) {
    display: none;
}

.use-content-item:nth-child(1) .use-item-left img {
    width: 29rem;
    height: 20.625rem;
}

.use-content-item:nth-child(2) .use-item-left img {
    width: 29.5rem;
    height: 22.625rem;
}

.use-content-item:nth-child(3) .use-item-left img {
    width: 25.75rem;
    height: 28.3125rem;
}

.use-content-item:nth-child(4) .use-item-left img {
    width: 21.375rem;
    height: 30rem;
}

.use-content-item:nth-child(5) .use-item-left img {
    width: 25rem;
    height: 29.375rem;
}

.use-content-item:nth-child(6) .use-item-left img {
    width: 28.125rem;
    height: 19.125rem;
}

.use-content-item:nth-child(7) .use-item-left img {
    width: 25.375rem;
    height: 26.625rem;
}

.use-item-right {
    margin-left: 4rem;
    flex: 1;
}

.item-right-title {
    margin-bottom: 2.625rem;
    font-size: 34px;
    font-weight: bold;
}

.item-right-ul {
    margin-top: 2.25rem;
}

.item-right-li {
    margin-top: 1rem;
    margin-left: 1rem;
    position: relative;
    font-weight: 600;
}

.item-right-li:before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -1rem;
    width: 0.4375rem;
    height: 0.4375rem;
    border-radius: 50%;
    background: #10B981;
}

.item-right-jump {
    display: flex;
    align-items: center;
    margin-top: 2.5rem;
    font-size: 18px;
    font-weight: bold;
}

.item-right-jump img {
    margin-left: 0.75rem;
    width: 1rem;
    height: 0.875rem;
}

.index-advantage {
    margin-top: 10.625rem;
}

.advantage-list {
    margin-top: 6rem;
    opacity: 0;
    transform: translateY(1.875rem);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.advantage-list.animate {
    opacity: 1;
    transform: translateY(0);
}

.advantage-list-top {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.advantage-list-bottom {
    margin-top: 3.75rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
}

.advantage-item {
    width: 28%;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.advantage-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.75rem;
    height: 3.75rem;
    border-radius: 50%;
    background: #EAF9F4;
}

.advantage-item-icon img {
    width: 1.75rem;
    height: 1.75rem;
}

.advantage-item-title {
    margin: 1.625rem 0 1.8125rem;
    width: 100%;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
}

.advantage-item-desc {
    font-size: 14px;
    text-align: center;
}

.index-trust {
    margin-top: 7.1875rem;
    width: 100%;
    height: 44.375rem;
    background: #1C7B6F;
    overflow: hidden;
}

.trust-content {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url("../assets/images/index/trust-left.png") no-repeat left top, url("../assets/images/index/trust-right.png") no-repeat 80% bottom;
    background-size: 37.25rem 40rem, 16.5625rem 100%;
    overflow: hidden;
}

.trust-content-left {
    color: #fff;
    white-space: nowrap;
}

.index-content-right {
    margin-left: 4.25rem;
}

.content-left-title {
    font-size: 48px;
    font-weight: bold;
}

.trust-title {
    font-size: 48px;
    font-weight: bold;
}

.trust-subTitle {
    margin-top: 2.5rem;
}

.trust-content-right {
    width: 45%;
    height: 100%;
    margin-left: 2.375rem;
    display: flex;
    position: relative;
    overflow: hidden;
    justify-content: flex-end;
}

.trust-right-list {
    width: 300%;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    overflow: visible;
    will-change: transform;
    transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.content-right-item {
    box-sizing: border-box;
    padding: 2.5rem 3.75rem;
    margin-right: 2.5rem;
    width: 34.625rem;
    background: #FFFFFF;
    border-radius: 1.25rem;
    flex-shrink: 0;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.character {
    display: flex;
    margin-bottom: 1.5rem;
}

.character-avatar {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    margin-right: 1rem;
    overflow: hidden;
}

.character-avatar img {
    width: 100%;
    height: 100%;
}

.character-name {
    font-size: 18px;
    font-weight: bold;
}

.character-position {
    font-size: 14px;
}

.trust-right-change {
    margin-top: 1.625rem;
    position: absolute;
    bottom: 15%;
    display: flex;
}

.change-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.625rem;
    height: 2.625rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    margin-right: 1rem;
    cursor: pointer;
}

.change-item img {
    width: 0.875rem;
    height: 0.75rem;
}

.change-item-img {
    transform: rotate(180deg);
}