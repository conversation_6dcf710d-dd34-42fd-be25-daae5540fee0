// service-marketing.js - 客服营销页面的交互和动画效果

$(document).ready(function() {
    // 初始化页面元素显示
    initPageElements();
    
    // 初始化滚动动画
    initScrollAnimations();
    
    // 立即激活首屏元素动画
    activateInitialAnimations();
});

// 初始化页面元素显示
function initPageElements() {
    // 确保所有主要元素可见
    $('.service-marketing-banner').css('visibility', 'visible');
    $('.service-marketing-workbenches').css('visibility', 'visible');
    $('.service-marketing-important').css('visibility', 'visible');
    $('.service-marketing-scheme').css('visibility', 'visible');
    $('.major-function').css('visibility', 'visible');
    
    // 移除可能导致隐藏的类
    $('.service-marketing-main').find('.hide, .hidden, .invisible').removeClass('hide hidden invisible');
}

// 立即激活首屏元素动画
function activateInitialAnimations() {
    // 立即为首屏元素添加animate类
    $('.banner-content').addClass('animate');
    $('.workbenches-bg').addClass('animate');
    $('.service-marketing-important').addClass('animate');
}

// 初始化滚动动画
function initScrollAnimations() {
    // 监听滚动事件，为进入视口的元素添加动画
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 为不同的部分添加不同的动画类
                entry.target.classList.add('animate');
                
                // 监测一次后停止监测该元素
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });
    
    // 监听重要功能项
    document.querySelectorAll('.important-item').forEach(item => {
        observer.observe(item);
    });
    
    // 监听营销方案项
    document.querySelectorAll('.marketing-scheme-item').forEach(item => {
        observer.observe(item);
    });
    
    // 监听workbenches-bg元素
    const workbenchesBg = document.querySelector('.workbenches-bg');
    if (workbenchesBg) {
        observer.observe(workbenchesBg);
    }
    
    // 监听service-marketing-important元素
    const serviceMarketingImportant = document.querySelector('.service-marketing-important');
    if (serviceMarketingImportant) {
        observer.observe(serviceMarketingImportant);
    }
} 