.about-us-banner {
    margin-top: 8.5625rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-us-banner img {
    width: 64.375rem;
    height: 22.625rem;
}

.about-us {
    position: relative;
    margin-top: 6.25rem;
    padding-bottom: 5rem;
    width: 100%;
    height: auto;
    background: linear-gradient(90deg, #E4F7FF 0%, #ECE1FF 100%);
    z-index: 1
}

.about-us::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);
    z-index: 2;
}

.about-us-detail {
    position: relative;
    z-index: 3;
}

.about-us-title {
    font-weight: bold;
    font-size: 48px;
}

.about-us-text {
    width: 100%;
}

.about-us-txt {
    margin-top: 2rem;
}

.info-box {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 9.625rem;
    margin-bottom: 2.75rem;
}

.info-box-content {
    box-sizing: border-box;
    position: relative;
    padding: 0 8.125rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 17.6875rem;
    background: #F5F8FF;
    border-radius: 3.75rem;
    z-index: 2;
}

.info-box-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.detail-top {
    font-weight: bold;
    font-size: 64px;
    color: #10b981;
}

.detail-data {
    font-size: 20px;
    color: #0E0E0E;
}

.a-img {
    position: absolute;
    right: 20%;
    z-index: 1;
    width: 22.5rem;
    height: 22.75rem;
}

.a-img img {
    width: 100%;
    height: 100%;
}

.info-communicate {
    margin-top: 11rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.info-communicate .left-title {
    font-weight: bold;
    font-size: 44px;
    color: #0E0E0E;
}

.info-communicate .left-detail {
    width: 34rem;
    margin-top: 2rem;
}

.communicate-right {
    width: 41.375rem;
    height: 27.875rem;
}

.communicate-right img {
    width: 100%;
    height: 100%;
}

.info-cooperate {
    margin-top: 9.375rem;
}

.cooperate-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 32px;
    color: #0E0E0E;
}

.cooperate-list {
    margin-top: 4.6875rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cooperate-item:nth-child(1) {
    width: 8.875rem;
    height: 1.5rem;
}

.cooperate-item:nth-child(2) {
    width: 4.875rem;
    height: 2.75rem;
}

.cooperate-item:nth-child(3) {
    width: 8.5625rem;
    height: 1.6875rem;
}

.cooperate-item:nth-child(4) {
    width: 6.5625rem;
    height: 1.9375rem;
}

.cooperate-item:nth-child(5) {
    width: 10rem;
    height: 1.875rem;
}

.cooperate-item:nth-child(6) {
    width: 8.125rem;
    height: 1.875rem;
}

.cooperate-item img {
    width: 100%;
    height: 100%;
}

.info-about-team {
    margin-top: 9.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.info-about-team .team-left .left-title {
    font-weight: bold;
    font-size: 14px;
    color: #10b981;
}

.info-about-team .team-left .left-detail {
    margin-top: 1.75rem;
    width: 34.0625rem;
}

.info-about-team .team-right {
    display: flex;
}

.team-right-text {
    margin-right: 2rem;
    width: 32.375rem;
}

.text-dh {
    margin-bottom: 3.875rem;
    width: 32.375rem;
    height: 2.75rem;
    text-align: right;
}

.text-dh img {
    margin-right: 3rem;
    width: 3.25rem;
    height: 100%;
}

.text-content {
    width: 100%;
    font-weight: bold;
    font-size: 40px;
    color: #0E0E0E;
    line-height: 3.125rem;
}

.team-right-line {
    width: 0.125rem;
    height: 21.25rem;
    background: linear-gradient(0deg, #2F40E9 11%, #7028D0 40%, #DD02A5 78%, #F99D1E 100%);
}

.info-team {
    margin-top: 9.375rem;
    margin-bottom: 6.25rem;
}

.info-team .team-title {
    margin-bottom: 4.125rem;
    font-weight: bold;
    font-size: 44px;
    color: #0E0E0E;
}

.team-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
}

.team-list .team-item {
    position: relative;
    cursor: pointer;
}

.team-list .team-item .img-wrapper {
    width: 17.75rem;
    height: 22.375rem;
    border-radius: 0.75rem;
    overflow: hidden;
}

.team-list .team-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform-origin: center;
    transition: transform 0.4s ease;
}

.team-list .team-item:hover img {
    transform: scale(1.08);
}

.team-list .team-item .team-name {
    font-weight: bold;
    font-size: 22px;
    color: #000000;
    margin-top: 2.25rem;
    margin-bottom: 1.25rem;
}

.team-list .team-item .team-job {
    font-weight: bold;
    font-size: 14px;
    color: #10b981;
}

.team-list .team-item:nth-child(n+5) {
    margin-top: 2.1875rem;
}

/* 添加team-item的动画样式 */
.team-item-hidden {
    opacity: 0;
    transform: translateY(30px);
}

.team-item-show {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* 确保team-item有基本的过渡效果 */
.team-item {
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

/* info-about-team的动画样式 */
.info-about-team-hidden {
    opacity: 0;
}

.info-about-team-hidden .team-left {
    opacity: 0;
    transform: translateX(-30px);
}

.info-about-team-hidden .team-right {
    opacity: 0;
    transform: translateX(30px);
}

.info-about-team-show {
    opacity: 1;
    transition: opacity 0.8s ease-out;
}

.info-about-team-show .team-left {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.info-about-team-show .team-right {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 0.8s ease-out 0.2s, transform 0.8s ease-out 0.2s;
}

/* info-box的动画样式 */
.info-box-hidden {
    opacity: 0;
}

.info-box-hidden .info-box-content {
    opacity: 0;
    transform: translateY(40px);
}

.info-box-hidden .a-img {
    opacity: 0;
    transform: scale(0.8);
}

.info-box-show {
    opacity: 1;
    transition: opacity 0.8s ease-out;
}

.info-box-show .info-box-content {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.info-box-show .a-img {
    opacity: 1;
    transform: scale(1);
    transition: opacity 1s ease-out 0.3s, transform 1s ease-out 0.3s;
}

/* about-us的动画样式 */
.about-us-hidden .about-us-title {
    opacity: 0;
    transform: translateY(20px);
}

.about-us-hidden .about-us-txt {
    opacity: 0;
    transform: translateY(30px);
}

.about-us-show .about-us-title {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.about-us-show .about-us-txt {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

/* 为每个段落添加延迟，形成依次显示的效果 */
.about-us-show .about-us-txt:nth-child(1) {
    transition-delay: 0.1s;
}

.about-us-show .about-us-txt:nth-child(2) {
    transition-delay: 0.2s;
}

.about-us-show .about-us-txt:nth-child(3) {
    transition-delay: 0.3s;
}

.about-us-show .about-us-txt:nth-child(4) {
    transition-delay: 0.4s;
}

.about-us-show .about-us-txt:nth-child(5) {
    transition-delay: 0.5s;
}