// 账号管理页面动画处理
function initAccountManagementAnimation() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 为不同区域的元素添加动画
                entry.target.classList.add('animate');

                // 如果是包含多个子元素的容器，为子元素添加延迟动画
                if (entry.target.classList.contains('process-content')) {
                    const items = entry.target.querySelectorAll('.process-item, .process-arrow');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 200 * index);
                    });
                }
                
                // 为核心功能区域添加动画
                if (entry.target.classList.contains('core-functions-list')) {
                    const items = entry.target.querySelectorAll('.core-functions-item');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 150 * index);
                    });
                }

                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });

    // 监听需要添加动画的元素
    const elementsToObserve = [
        '.number-maintenance-banner .banner-content',
        '.number-maintenance-process .process-content',
        '.core-functions .core-functions-list',
        '.management-scheme .content-item',
        '.major-function .major-function-content'
    ];

    elementsToObserve.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            observer.observe(element);
        });
    });
}

// 当DOM加载完成后初始化动画
document.addEventListener('DOMContentLoaded', function() {
    initAccountManagementAnimation();
});