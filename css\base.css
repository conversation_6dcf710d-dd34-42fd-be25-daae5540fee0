/* 把我们所有标签的内外边距清零 */

* {
    margin: 0;
    padding: 0;
    /* css3盒子模型 */
    box-sizing: border-box;
}

/* 设置 rem 基准，以 1rem 为基准单位 */
html {
    font-size: 1rem;
}

/* 针对不同设备的响应式设置 */
@media screen and (max-width: 100rem) {
    html {
        font-size: 14px;
    }
}

@media screen and (max-width: 75rem) {
    html {
        font-size: 12px;
    }
}

@media screen and (max-width: 48rem) {
    html {
        font-size: 10px;
    }
}

/* em 和 i 斜体的文字不倾斜 */

em,
i {
    font-style: normal;
}

/* 去掉li 的小圆点 */

li {
    list-style: none;
}

img {
    /* border 0 照顾低版本浏览器 如果 图片外面包含了链接会有边框的问题 */
    border: 0;
    /* 取消图片底侧有空白缝隙的问题 */
    vertical-align: middle;
}

button {
    /* 当我们鼠标经过button 按钮的时候，鼠标变成小手 */
    cursor: pointer;
}

a {
    color: #161C2D;
    text-decoration: none;
}


button,
input {
    /* "\5B8B\4F53" 就是宋体的意思 这样浏览器兼容性比较好 */
    font-family: PingFangSC-Regular, PingFang SC, Microsoft YaHei, Heiti SC,
    tahoma, arial, Hiragino Sans GB, "\5B8B\4F53", sans-serif;
    /* 默认有灰色边框我们需要手动去掉 */
    border: 0;
    outline: none;
}

body {
    /* CSS3 抗锯齿形 让文字显示的更加清晰 */
    -webkit-font-smoothing: antialiased;
    background-color: #fff;
    font: 16px/1.5 HarmonyOS Sans, -apple-system, blinkmacsystemfont, Segoe UI, roboto, Helvetica Neue, arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji, "\5B8B\4F53", sans-serif !important;
    color: #161C2D;
}

.hide,
.none {
    display: none;
}

/* 清除浮动 */

.clearfix:after {
    visibility: hidden;
    clear: both;
    display: block;
    content: ".";
    height: 0;
}

.clearfix {
    zoom: 1;
}

/*多行文本注释 */

.line-clamp-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
}

/* 版心 */

.w {
    width: 75rem;
    height: 100%;
    margin: 0 auto;
}

/* 标题大小 */

.base-title {
    width: 100%;
    text-align: center;
    font-size: 48px;
    font-weight: bold;
}

.base-sub-title {
    margin-top: 2.25rem;
    width: 100%;
    text-align: center;
    font-size: 18px;
}

.bg-title {
    border-radius: 0.5625rem;
    padding: 0 0.625rem;
    display: inline-block;
    background-color: #CEFB62;
}

/* 侧边回到顶部按钮 */
.back-to-top {
    position: fixed;
    right: 2.5rem;
    bottom: 8.125rem;
    width: 2.25rem;
    height: 2.25rem;
    background: #333333;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.2);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-0.1875rem);
    box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.3);
}

.back-to-top img {
    width: 1rem;
    height: 1.125rem;
}

.telegram-btn {
    position: fixed;
    right: 2.5rem;
    bottom: 3.75rem;
    width: 11.9375rem;
    height: 3.6875rem;
    background: #28A8E9;
    border-radius: 1.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 999;
    box-shadow: 0 0.125rem 0.625rem rgba(0, 0, 0, 0.2);
    font-weight: bold;
    font-size: 1rem;
    color: #FFFFFF;
}

.telegram-btn img {
    margin-right: 0.625rem;
    width: 1.5rem;
    height: 1.125rem;
}

.telegram-btn:hover {
    transform: translateY(-0.1875rem);
    box-shadow: 1px 1px rgba(0, 0, 0, 0.3);
}

.telegram-powered {
    position: fixed;
    display: flex;
    align-items: center;
    right: 4.375rem;
    bottom: 1.875rem;
    font-size: 12px;
    color: #0E0E0E;
    z-index: 999;
    text-align: center;
    width: 7.5rem;
    white-space: nowrap;
}

.telegram-powered img {
    margin-right: 0.625rem;
    width: 0.625rem;
    height: 0.875rem;
}

.powered-by {
    font-weight: bold;
    font-size: 13px;
    margin-right: 0.1rem;
}

.powered-by-a2c {
    font-weight: bold;
    font-size: 13px;
    color: #0E0E0E;
    background: linear-gradient(90deg, #2F40E9 10.693359375%, #7028D0 39.892578125%, #DD02A5 78.1494140625%, #F99D1E 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}