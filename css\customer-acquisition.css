.customer-acquisition-main {
    margin-top: 10rem;
}

.customer-acquisition-banner {
    width: 100%;
    height: auto;
    padding-bottom: 4rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-desc {
    font-size: 18px;
    margin-top: 2.125rem;
}

.banner-left .banner-title {
    font-size: 3.5rem;
    font-weight: bold;
}

.banner-title-first {
    color: #10B981;
}

.banner-right {
    width: 32.875rem;
    height: 32.25rem;
}

.banner-right img {
    width: 100%;
    height: 100%;
}

.customer-acquisition-benefit {
    margin-top: 6.25rem;
    margin-bottom: 6.875rem;
}

.benefit-content {
    margin-top: 4.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

}

.benefit-item {
    padding: 1.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 31.5%;
    height: 26.25rem;
    background: #EAEDF0;
    border-radius: 0.5rem;
    opacity: 0;
    transition: all 0.8s ease;
}

.benefit-item img {
    width: 5.125rem;
    height: 5.125rem;
}

.process-item-title {
    margin-top: 3.5rem;
    margin-bottom: 1.5rem;
    font-size: 20px;
    font-weight: bold;
}

.process-item-desc {
    text-align: center;
    font-size: 14px;
}

.content-item {
    background: #F8F9FD !important;
    padding: 2.625rem 0 !important;
    opacity: 0;
    transition: all 0.8s ease;
}

.difference {
    width: 31.5rem !important;
    height: 31.5rem !important;
}
/* 动画效果 */
.benefit-item, .content-item, .major-function-content-right-item {
    opacity: 0;
    transition: all 0.8s ease;
}

/* 淡入动画 */
.animate-fade-in {
    animation: fadeIn 0.8s forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 滑入动画 */
.content-item {
    transform: translateX(-50px);
}

.content-item:nth-child(even) {
    transform: translateX(50px);
}

.animate-slide-in {
    animation: slideIn 0.8s forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.content-item:nth-child(even).animate-slide-in {
    animation: slideInRight 0.8s forwards;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 向上淡入动画 */
.major-function-content-right-item {
    transform: translateY(30px);
}

.animate-fade-up {
    animation: fadeUp 0.8s forwards;
}

@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 为动画添加延迟，使其错开显示 */
.benefit-item:nth-child(1), 
.major-function-content-right-item:nth-child(1) {
    animation-delay: 0.1s;
}

.benefit-item:nth-child(2), 
.major-function-content-right-item:nth-child(2) {
    animation-delay: 0.3s;
}

.benefit-item:nth-child(3), 
.major-function-content-right-item:nth-child(3) {
    animation-delay: 0.5s;
}

.content-item:nth-child(1) {
    animation-delay: 0.1s;
}

.content-item:nth-child(2) {
    animation-delay: 0.3s;
}

.content-item:nth-child(3) {
    animation-delay: 0.5s;
}

.content-item:nth-child(4) {
    animation-delay: 0.7s;
}
