$(document).ready(function() {
    // 初始化时隐藏所有回答内容
    $('.answer-item-content').hide();

    // 点击问题标题时的处理
    $('.answer-item-top').on('click', function() {
        // 获取当前点击的问题对应的内容元素
        const $content = $(this).next('.answer-item-content');
        // 获取当前问题的图标元素
        const $icon = $(this).find('.answer-item-icon');
        
        // 如果内容是隐藏的，则显示它并改变图标为 "-"
        // 否则隐藏内容并改变图标为 "+"
        if ($content.is(':hidden')) {
            $content.slideDown(300);
            $icon.text('-');
            $(this).addClass('active'); // 添加active类
        } else {
            $content.slideUp(300);
            $icon.text('+');
            $(this).removeClass('active'); // 移除active类
        }
    });
});
