.price-plan-main {
    margin-top: 5rem;
}

.price-plan-container {
    padding-top: 5rem;
    padding-bottom: 7.125rem;
    width: 100%;
    background: #F8F9FD;
}

.price-plan-title {
    width: 100%;
    text-align: center;
    font-size: 56px;
    font-weight: bold;
}

.price-plan-sub-title {
    margin-top: 2.5rem;
    width: 100%;
    text-align: center;
    font-size: 18px;
}

.price-plan-tabs {
    margin-top: 3.5rem;
    display: flex;
    justify-content: center;
    width: 100%;
}

.tabs-list {
    display: flex;
    align-items: center;
    width: 20.4375rem;
    height: 3.5rem;
    background: #E5E4EB;
    border-radius: 1.75rem;
}

.tab-item {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.control-tab {
    width: 7.75rem;
    height: 100%;
}

.api-tab {
    flex: 1;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.api-a {
    color: #00A8FF
}

.api-p {
    color: #FF4757
}

.api-i {
    color: #2ED573
}

.api-tab.api-active > .api-a,
.api-tab.api-active > .api-p,
.api-tab.api-active > .api-i {
    color: #FBD798;
}

.hot-tag {
    margin-left: 0.5rem;
    width: 2.875rem;
    height: 1.5rem;
    background: #FB3B26;
    border-radius: 0.75rem;
    color: #fff;
    text-align: center;
}

.tabs-list .active {
    background: #10B981;
    border-radius: 1.75rem;
    color: #fff;
}

.tabs-list .api-active {
    background: linear-gradient(90deg, #3C3835 0%, #4B4542 54%, #0C0505 100%);
    border-radius: 1.75rem;
    color: #FBD798;
}

.tabs-content {
    width: 100%;
    margin-top: 5rem;
}

.content-control-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}


.content-item {
    padding: 2.5rem 2.625rem 3.5rem;
    width: 30%;
    border-radius: 0.625rem;
    border: 2px solid #AFCFC2;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.content-item .content-title {
    font-weight: bold;
    font-size: 30px;
    color: #10B981;
}

.content-item .content-sub-title {
    font-size: 18px;
    color: #515151;
}

.content-price {
    margin-top: 2.25rem;
    color: #111111;
}

.price-unit {
    font-size: 20px;
}

.content-price .price-money {
    font-size: 32px;
    font-weight: bold;
}

.content-item .content-price .price-moon {
    color: #2C2C2C;
    font-size: 18px;
}

.recharge-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1.875rem;
    width: 17.5rem;
    height: 2.875rem;
    font-size: 14px;
    background: #10B981;
    border-radius: 0.5rem;
    color: #fff;
    cursor: pointer;
}

/*
.content-item:hover {
    box-shadow: 0 8px 22px 0 rgba(2, 10, 24, 0.16);
    border: 2px solid #23AD7F;
}*/

.content-api-list {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-api-item {
    padding-top: 2.625rem;
    width: 30%;
    background: linear-gradient(110deg, #3C3835 0%, #4B4542 54%, #0C0505 100%);
    border-radius: 0.625rem 0.625rem 1.3125rem 1.3125rem;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.content-api-item .api-item-top {
    padding: 0 2.625rem;
}

.content-api-item .content-title {
    font-weight: bold;
    font-size: 30px;
    background: linear-gradient(0deg, #FFF5EC 0%, #FFE3CB 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.content-api-item .content-sub-title {
    font-size: 18px;
    color: #BBA79A;
}

.content-api-item .content-price {
    margin-top: 2.25rem;
    color: #FBD798;
}

.content-api-item .content-price .price-num {
    font-size: 20px;
}

.content-api-item .content-price .price-money {
    font-size: 32px;
    font-weight: bold;
}

.content-api-item .content-price .price-moon {
    font-size: 18px;
}

.api-recharge-btn {
    margin-top: 1.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 17.5rem;
    height: 2.875rem;
    background: linear-gradient(90deg, #FFF3E9 0%, #FFD3AF 100%);
    border-radius: 0.5rem;
    font-size: 14px;
    color: #6B3A00;
    cursor: pointer;
}

.content-api-tips {
    padding: 1.875rem 0 1.875rem 2.625rem;
    width: 100%;
    margin-top: 2.5rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    background: #FEFAF5;
    border-radius: 1.25rem;
    box-shadow: 0 0.5rem 1.375rem 0 rgba(2, 10, 24, 0.16);
}

.api-support-icon {
    width: 1.0625rem;
    height: 1.0625rem;
}

.content-tips {
    margin-top: 2.5rem;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.tips-item {
    display: flex;
    align-items: center;
}

.tips-item:nth-child(n) {
    margin-right: 2.8125rem;
}

.tips-item:nth-child(n+3) {
    margin-top: 1.875rem;
}

.tips-item img {
    margin-right: 0.75rem;
}

.support-icon {
    width: 1.0625rem;
    height: 1rem;
}

.notsupport-icon {
    width: 0.8125rem;
    height: 0.8125rem;
}

.price-plan-table {
    margin-top: 6.25rem;
}

.base-sub-title {
    margin-top: 2.25rem;
    text-align: center;
}

.table-content {
    margin-top: 5rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.consultation-content {
    margin-top: 13.25rem;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 24.5rem;
    background: #F5F6F7;
    border-radius: 0.75rem;
}

.consultation-content-left {
    display: flex;
    align-items: flex-end;
    width: 50%;
    height: 100%;
}

.consultation-content-left img {
    width: 33rem;
    height: 30.8125rem;
}

.consultation-content-right {
    flex: 1;
}

.consultation-title {
    font-size: 36px;
    font-weight: bold;
}

.consultation-desc {
    font-size: 18px;
}

.consultation-desc-one {
    margin-top: 2.125rem;
}

.consultation-btn {
    margin-top: 2.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    width: 9.5rem;
    height: 2.8125rem;
    background: #10B981;
    border-radius: 0.5rem;
    font-size: 14px;
    color: #FFFFFF;
    cursor: pointer;
}