$(document).ready(function () {

    // 使用Intersection Observer API监听元素进入视口
    function setupAnimationObserver() {
        // 创建观察者配置
        const options = {
            root: null, // 使用视口作为根
            rootMargin: '0px',
            threshold: 0.15 // 当元素15%进入视口时触发
        };

        // 创建观察者实例
        const observer = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                // 当元素进入视口时
                if (entry.isIntersecting) {
                    // 添加动画类
                    entry.target.classList.add('animate');
                    // 元素已经显示动画，不再需要观察
                    observer.unobserve(entry.target);
                }
            });
        }, options);

        // 观察所有需要动画的元素
        const animatedElements = [
            ...document.querySelectorAll('.service-list'),
            ...document.querySelectorAll('.ability-list'),
            ...document.querySelectorAll('.advantage-list')
        ];

        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }

    // 初始化动画观察者
    setupAnimationObserver();

    // 信息选项卡切换功能
    const $tabItems = $('.tab-item');
    const $contentItems = $('.information-content-item');

    // 默认显示第一个选项卡内容
    if ($tabItems.length > 0) {
        $tabItems.eq(0).addClass('active');

        // 为每个选项卡添加点击事件
        $tabItems.each(function (index) {
            $(this).on('click', function () {
                // 移除所有选项卡的active类
                $tabItems.removeClass('active');

                // 为当前点击的选项卡添加active类
                $(this).addClass('active');

                // 隐藏所有内容
                $contentItems.hide();

                // 显示对应的内容
                $contentItems.eq(index).css('display', 'flex');

                // 切换图标状态
                updateIcons();
            });
        });

        // 更新所有图标的状态
        function updateIcons() {
            // 存储每个tab的原始图标类名
            const iconTypes = ['txt', 'video', 'picture', 'interactive', 'file'];

            $tabItems.each(function () {
                const $iconElement = $(this).find('.item-icon');
                if ($iconElement.length === 0) return;

                // 确定这个图标是哪种类型
                let iconType = '';
                for (let type of iconTypes) {
                    if ($iconElement.hasClass(`${type}-icon`) ||
                        $iconElement.hasClass(`${type}-active`)) {
                        iconType = type;
                        break;
                    }
                }

                if (iconType) {
                    // 移除所有可能的active状态
                    $.each(iconTypes, function (i, type) {
                        $iconElement.removeClass(`${type}-active`);
                    });

                    if ($(this).hasClass('active')) {
                        // 激活状态：移除-icon类，添加-active类
                        $iconElement.removeClass(`${iconType}-icon`);
                        $iconElement.addClass(`${iconType}-active`);
                    } else {
                        // 非激活状态：移除-active类，添加-icon类
                        $iconElement.removeClass(`${iconType}-active`);
                        $iconElement.addClass(`${iconType}-icon`);
                    }
                }
            });
        }

        // 初始化图标状态
        updateIcons();
    }

    // 功能选项卡切换功能
    const $useTabItems = $('.use-tab-item');
    const $useContentItems = $('.use-content-item');

    if ($useTabItems.length > 0) {
        // 默认第一个选项卡为激活状态
        if ($('.use-tab-item.use-active').length === 0) {
            $useTabItems.eq(0).addClass('use-active');
        }

        // 为每个选项卡添加点击事件
        $useTabItems.each(function (index) {
            $(this).on('click', function () {
                // 移除所有选项卡的use-active类
                $useTabItems.removeClass('use-active');

                // 为当前点击的选项卡添加use-active类
                $(this).addClass('use-active');

                // 隐藏所有内容
                $useContentItems.hide();

                // 显示对应的内容
                $useContentItems.eq(index).css('display', 'flex');
            });
        });
    }
    
    // 信任区域内容切换功能 - 卡片式平移效果
    const $trustList = $('.trust-right-list');
    const $trustChangeButtons = $('.change-item');
    let isAnimating = false; // 防止动画过程中重复点击
    let currentIndex = 0; // 当前显示的索引

    // 初始化：获取所有项目
    const $trustItems = $('.content-right-item');
    const itemCount = $trustItems.length;

    // 计算容器宽度（单个卡片的显示宽度）
    const getContainerWidth = function() {
        return $('.trust-content-right').width();
    };

    // 计算单个卡片的实际宽度（包括margin）
    const getItemWidth = function() {
        return $('.content-right-item').outerWidth(true);
    };

    // 初始化位置
    function initializePosition() {
        const containerWidth = getContainerWidth();
        $trustList.css('transform', `translateX(0) translateY(-50%)`);
    }

    // 卡片式平移到指定索引
    function slideTo(index, direction) {
        if (isAnimating) return;
        isAnimating = true;

        // 确保索引在有效范围内
        if (index < 0) index = itemCount - 1;
        if (index >= itemCount) index = 0;

        const itemWidth = getItemWidth();

        // 计算平移距离：基于卡片实际宽度（包括margin）
        const translateX = -itemWidth * index;

        $trustList.css('transform', `translateX(${translateX}px) translateY(-50%)`);

        // 动画完成后更新索引
        setTimeout(function() {
            currentIndex = index;
            isAnimating = false;
        }, 800);
    }

    // 为左右切换按钮添加点击事件
    $trustChangeButtons.each(function(index) {
        $(this).on('click', function() {
            // 左箭头点击(index=0)，向右移动（显示前一个）
            if (index === 0) {
                const prevIndex = (currentIndex - 1 + itemCount) % itemCount;
                slideTo(prevIndex, 'right');
            }
            // 右箭头点击(index=1)，向左移动（显示后一个）
            else {
                const nextIndex = (currentIndex + 1) % itemCount;
                slideTo(nextIndex, 'left');
            }
        });
    });

    // 初始化位置
    initializePosition();

    // 窗口大小改变时重新计算位置
    $(window).on('resize', function() {
        initializePosition();
        const itemWidth = getItemWidth();
        const translateX = -itemWidth * currentIndex;
        $trustList.css('transform', `translateX(${translateX}px) translateY(-50%)`);
    });

    // Sprites图动画功能 - 使用anime.js
    const spriteElement = document.getElementById('sprite-animation');
    if (spriteElement && typeof anime !== 'undefined') {
        // sprites图布局：1行 × 23列
        const totalFrames = 23; // 总共23帧
        const frameWidth = 811; // 每帧的宽度

        // 创建一个对象来保存当前帧数
        const frameObj = { frame: 0 };

        // 使用anime.js动画帧数值
        anime({
            targets: frameObj,
            frame: totalFrames - 1, // 从0动画到22
            duration: 23 * 150, // 总时长：23帧 × 800ms
            easing: 'linear',
            loop: true,
            round: 1, // 确保帧数是整数
            update: function () {
                // 计算背景位置
                const backgroundPositionX = -(Math.floor(frameObj.frame) * frameWidth);
                spriteElement.style.backgroundPosition = `${backgroundPositionX}px 0`;
            }
        });
    }
});