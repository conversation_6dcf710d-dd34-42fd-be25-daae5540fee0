$(document).ready(function() {
    // 检测元素是否在视口内
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
            rect.bottom >= 0
        );
    }

    // 为元素添加动画类
    function addAnimationClass() {
        // 为benefit-item添加动画
        $('.benefit-item').each(function() {
            if (isElementInViewport(this)) {
                $(this).addClass('animate-fade-in');
            }
        });

        // 为content-item添加动画
        $('.content-item').each(function() {
            if (isElementInViewport(this)) {
                $(this).addClass('animate-slide-in');
            }
        });

        // 为major-function-content-right-item添加动画
        $('.major-function-content-right-item').each(function() {
            if (isElementInViewport(this)) {
                $(this).addClass('animate-fade-up');
            }
        });
    }

    // 页面加载和滚动时检查元素
    $(window).on('scroll load', addAnimationClass);
}); 