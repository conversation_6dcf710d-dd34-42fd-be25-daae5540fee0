header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    background: #FFF;
    width: 100%;
    height: 5rem;
    color: #161C2D;
    text-align: center;
}

header nav {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    width: 10.75rem;
    height: 2.5rem;
}

.logo img {
    width: 100%;
    height: 100%;
}

.nav-list {
    display: flex;
    align-items: center;
    height: 100%;
}

.nav-list li {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2.375rem;
    height: 100%;
    border-radius: 0.375rem;
}


.arrow {
    width: 1.125rem;
    height: 0.6875rem;
    margin-left: 0.625rem;
}

.nav-list li a {
    display: flex;
    align-items: center;
    font-weight: bold;
    color: #161C2D;
}

.features {
    font-weight: bold;
    cursor: pointer;
}

.login {
    width: 5.5rem;
    height: 2.75rem !important;
    background: #10B981;
    cursor: pointer;
}

.on-trial {
    width: 7.5rem;
    height: 2.75rem !important;
    border: 1px solid #10B981;
    cursor: pointer;
    margin-right: 0 !important;
}

.login a {
    color: #FFF !important;
}

.on-trial a {
    color: #10B981 !important;
}


.sub-header {
    display: none;
    background: #fff;
    width: 100%;
    height: 8.375rem;
    border-top: 1px solid #e5e5e5;
    box-shadow: 0 4px 23px -8px rgba(0, 49, 43, 0.17);
}

.nav-list li.features:hover ~ .sub-header,
.sub-header:hover {
    display: block;
}

.sub-header .w,
.sub-header-list {
    height: 100%;
}

.sub-header-list {
    display: flex;
    justify-content: center;
    align-items: center;
}

.sub-header-list li {
    cursor: pointer;
    margin-right: 1.875rem;
    padding: 1.125rem 2.25rem;
}

.sub-item .item-top {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.sub-item div:nth-child(2) {
    margin-top: 0.625rem;
    font-size: 14px;
    color: #4D4D4D
}

.sub-item .item-top img {
    margin-right: 0.375rem;
}

.sub-item:nth-child(1) .item-top img {
    width: 1rem;
    height: 1rem;
}

.sub-item:nth-child(2) .item-top img {
    width: 1.125rem;
    height: 1.25rem;
}

.sub-item:nth-child(3) .item-top img {
    width: 1.125rem;
    height: 1.25rem;
}

.sub-item:nth-child(4) .item-top img {
    width: 1rem;
    height: 1rem;
}

.sub-item:nth-child(5) .item-top img {
    width: 1.125rem;
    height: 1.125rem;
}

.sub-item:nth-child(6) .item-top img {
    width: 1.0625rem;
    height: 1rem;
}

.sub-item:nth-child(7) .item-top img {
    width: 1.1875rem;
    height: 1.125rem;
}

.sub-item:hover {
    background: #EDF5F2;
    border-radius: 0.375rem;
}

/* 添加item-top的hover效果，切换图片 */
.sub-header-list .sub-item:hover .item-top img[src*="whatsapp.svg"] {
    content: url("../assets/images/common/whatsapp-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="customer.png"] {
    content: url("../assets/images/common/customer-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="drainage.png"] {
    content: url("../assets/images/common/drainage-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="manage.png"] {
    content: url("../assets/images/common/manage-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="staff.png"] {
    content: url("../assets/images/common/staff-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="mass-send.png"] {
    content: url("../assets/images/common/mass-send-hover.png");
}

.sub-header-list .sub-item:hover .item-top img[src*="maintenance.png"] {
    content: url("../assets/images/common/maintenance-hover.png");
}