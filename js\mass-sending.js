// WhatsApp群发页面动画处理
function initMassSendingAnimation() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                // 为不同区域的元素添加动画
                entry.target.classList.add('animate');

                // Banner区域动画
                if (entry.target.classList.contains('banner-content')) {
                    // 标题动画
                    const titles = entry.target.querySelectorAll('.banner-title');
                    titles.forEach((title, index) => {
                        setTimeout(() => {
                            title.classList.add('animate');
                        }, 200 * index);
                    });

                    // 描述动画
                    const desc = entry.target.querySelector('.banner-desc');
                    if (desc) {
                        setTimeout(() => {
                            desc.classList.add('animate');
                        }, 600);
                    }

                    // 右侧图片动画
                    const bannerRight = entry.target.querySelector('.banner-right');
                    if (bannerRight) {
                        setTimeout(() => {
                            bannerRight.classList.add('animate');
                        }, 800);
                    }
                }

                // 场景内容动画
                if (entry.target.classList.contains('scene-content')) {
                    const items = entry.target.querySelectorAll('.scene-content-item');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 300 * index);
                    });
                }

                // 核心功能区域动画
                if (entry.target.classList.contains('core-functions-content')) {
                    const rightItems = entry.target.querySelectorAll('.core-functions-content-right-item');
                    rightItems.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 200 * index);
                    });
                }

                // 优势区域动画
                if (entry.target.classList.contains('mass-sending-advantage-content-top')) {
                    const items = entry.target.querySelectorAll('.content-top-item');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 200 * index);
                    });
                }

                if (entry.target.classList.contains('mass-sending-advantage-content-bottom')) {
                    const items = entry.target.querySelectorAll('.content-bottom-item');
                    items.forEach((item, index) => {
                        setTimeout(() => {
                            item.classList.add('animate');
                        }, 200 * index);
                    });
                }

                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.2
    });

    // 监听需要添加动画的元素
    const elementsToObserve = [
        '.mass-sending-banner .banner-content',
        '.mass-sending-scene .scene-content',
        '.core-functions .core-functions-content',
        '.management-scheme .content-item',
        '.mass-sending-advantage-content-top',
        '.mass-sending-advantage-content-bottom',
        '.major-function .major-function-content'
    ];

    elementsToObserve.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            observer.observe(element);
        });
    });
}

// 数字计数动画
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const range = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + range * easeOutQuart);

        element.textContent = current.toLocaleString();

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

// 初始化数字计数动画
function initNumberCounters() {
    const numberElements = document.querySelectorAll('.number-counter');

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const finalNumber = parseInt(element.dataset.number) || parseInt(element.textContent.replace(/,/g, ''));

                animateNumber(element, 0, finalNumber, 2000);
                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.5
    });

    numberElements.forEach(element => {
        observer.observe(element);
    });
}

// 添加特殊动画效果
function addSpecialAnimations() {
    // 为banner右侧图片添加浮动动画
    const bannerRight = document.querySelector('.banner-right img');
    if (bannerRight) {
        bannerRight.classList.add('float-animation');
    }

    // 为场景图标添加脉冲动画
    const sceneIcons = document.querySelectorAll('.scene-content-item-icon');
    sceneIcons.forEach((icon, index) => {
        setTimeout(() => {
            icon.classList.add('pulse-animation');
        }, 1000 + index * 500);
    });

    // 为优势图标添加旋转动画
    const advantageIcons = document.querySelectorAll('.mass-content-item-icon');
    advantageIcons.forEach((icon, index) => {
        setTimeout(() => {
            icon.classList.add('rotate-animation');
        }, 2000 + index * 300);
    });
}

// 鼠标跟随效果已移除

// 滚动视差效果（优化版本，确保图片完整显示）
function initParallaxEffect() {
    // 使用节流函数优化性能
    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.banner-right img');

        parallaxElements.forEach(element => {
            const speed = 0.1; // 进一步减小速度
            const yPos = scrolled * speed;
            // 限制移动范围更小，确保图片不会移出容器
            const limitedYPos = Math.max(-20, Math.min(20, yPos));
            element.style.transform = `translateY(${limitedYPos}px)`;
        });

        ticking = false;
    }

    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    });
}

// 创建粒子效果
function createParticles() {
    const banner = document.querySelector('.mass-sending-banner');
    if (!banner) return;

    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles-bg';
    banner.appendChild(particlesContainer);

    function createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);

        // 移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 25000);
    }

    // 创建初始粒子
    for (let i = 0; i < 20; i++) {
        setTimeout(() => createParticle(), i * 500);
    }

    // 持续创建粒子
    setInterval(createParticle, 1000);
}

// 添加进度条动画
function addProgressBars() {
    const advantageItems = document.querySelectorAll('.content-top-item, .content-bottom-item');

    advantageItems.forEach(item => {
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';

        const progressFill = document.createElement('div');
        progressFill.className = 'progress-fill';
        progressBar.appendChild(progressFill);

        item.appendChild(progressBar);
    });
}

// 添加卡片翻转效果
function addFlipCards() {
    const sceneItems = document.querySelectorAll('.scene-content-item-high, .scene-content-item-low');

    sceneItems.forEach(item => {
        const flipCard = document.createElement('div');
        flipCard.className = 'flip-card';

        const flipCardInner = document.createElement('div');
        flipCardInner.className = 'flip-card-inner';

        const flipCardFront = document.createElement('div');
        flipCardFront.className = 'flip-card-front';
        flipCardFront.innerHTML = item.innerHTML;

        const flipCardBack = document.createElement('div');
        flipCardBack.className = 'flip-card-back';
        flipCardBack.innerHTML = `
            <h3>了解更多</h3>
            <p>点击查看详细功能介绍</p>
            <button style="background: white; color: #10B981; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">立即体验</button>
        `;

        flipCardInner.appendChild(flipCardFront);
        flipCardInner.appendChild(flipCardBack);
        flipCard.appendChild(flipCardInner);

        item.innerHTML = '';
        item.appendChild(flipCard);
    });
}

// 添加打字机效果
function addTypewriterEffect() {
    const title = document.querySelector('.banner-title-first');
    if (!title) return;

    const text = title.textContent;
    title.textContent = '';

    let i = 0;
    const typeInterval = setInterval(() => {
        title.textContent += text.charAt(i);
        i++;
        if (i >= text.length) {
            clearInterval(typeInterval);
        }
    }, 100);
}

// 添加滚动触发的数字动画
function addScrollTriggeredAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;

                // 添加进度条动画
                const progressFill = element.querySelector('.progress-fill');
                if (progressFill) {
                    progressFill.style.animationPlayState = 'running';
                }

                // 添加图标旋转
                const icon = element.querySelector('.mass-content-item-icon');
                if (icon) {
                    icon.style.animation = 'rotate 2s ease-in-out';
                }

                observer.unobserve(element);
            }
        });
    }, {
        threshold: 0.3
    });

    const animatedElements = document.querySelectorAll('.content-top-item, .content-bottom-item');
    animatedElements.forEach(element => {
        observer.observe(element);
    });
}

// 防止页面加载时出现滚动条
function preventInitialOverflow() {
    // 确保body和html不会产生水平滚动
    document.documentElement.style.overflowX = 'hidden';
    document.body.style.overflowX = 'hidden';

    // 确保所有动画元素在初始状态下不会超出视口
    const animatedElements = document.querySelectorAll('[style*="translateX"]');
    animatedElements.forEach(element => {
        element.style.maxWidth = '100%';
    });
}

// 当DOM加载完成后初始化所有动画
document.addEventListener('DOMContentLoaded', function() {
    // 首先防止溢出
    preventInitialOverflow();

    initMassSendingAnimation();
    initNumberCounters();

    // 延迟添加特殊动画效果
    setTimeout(() => {
        addSpecialAnimations();
        createParticles();
        addProgressBars();
        addScrollTriggeredAnimations();
    }, 1000);

    // 延迟添加更复杂的效果
    setTimeout(() => {
        addTypewriterEffect();
    }, 2000);

    // 鼠标跟随效果已移除

    // 初始化视差效果
    initParallaxEffect();
});

// 窗口大小改变时重新检查溢出
window.addEventListener('resize', () => {
    preventInitialOverflow();
});