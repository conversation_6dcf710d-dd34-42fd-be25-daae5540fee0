.account-management-main {
    margin-top: 10rem;
}

.account-management-banner {
    width: 100%;
    height: auto;
    padding-bottom: 4rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.banner-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-desc {
    font-size: 18px;
    margin-top: 2rem;
}

.banner-left .banner-title {
    font-size: 56px;
    font-weight: bold;
}

.banner-title-first {
    color: #10B981;
}

.banner-right {
    width: 31.25rem;
    height: 22.625rem;
}

.banner-right img {
    width: 100%;
    height: 100%;
}

.account-management-process {
    margin-top: 6.25rem;
}

.process-content {
    margin-top: 4.5rem;
    margin-bottom: 6.875rem;
    display: flex;
    align-items: center;
    justify-content: space-around;
    opacity: 0;
    transition: opacity 0.8s ease-out;
}

.process-content.animate {
    opacity: 1;
}

.process-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease-out;
}

.process-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.process-arrow {
    width: 5rem;
    height: 1rem;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease-out;
}

.process-item.animate, .process-arrow.animate {
    opacity: 1;
    transform: translateY(0);
}

.process-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.5rem;
    height: 3.5rem;
    font-weight: bold;
    font-size: 22px;
    color: #008069;
    background: #EAF9F4;
    border-radius: 50%;;
}

.process-item-txt {
    margin-top: 0.875rem;
    font-size: 20px;
    font-weight: bold;
}


.content-item {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.content-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.major-function-content {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.major-function-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.major-function-content-right-item {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease-out;
}

.major-function-content-right-item.animate {
    opacity: 1;
    transform: translateX(0);
}