.api-detail-main {
    margin-top: 5rem;
}

.api-detail-banner {
    width: 100%;
    height: 40rem;
    background: url('../assets/images/common/banner.png') no-repeat center center;
    background-size: 100% 100%;
}

.banner-content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.banner-left {
    flex: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.banner-left .banner-title {
    font-size: 56px;
    font-weight: bold;
}

.banner-whatsapp {
    color: #10B981
}

.banner-a {
    color: #00A8FF;
}

.banner-p {
    color: #FF4757;
}

.banner-i {
    color: #2ED573;
}

.banner-desc {
    font-size: 18px;
    margin-top: 2.125rem;
}

.banner-right {
    width: 43.3125rem;
    height: 33.5625rem;
}

.banner-right img {
    width: 100%;
    height: 100%;
}

.api-detail-construct {
    margin-top: 6.25rem;
}

.construct-list {
    margin-top: 5rem;
    display: flex;
    justify-content: space-between;
}

.construct-item {
    width: 26%;
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.construct-item.animate {
    opacity: 1;
    transform: translateY(0);
}

.construct-item-img {
    width: 100%;
    text-align: center;
}

.construct-icon1 {
    width: 10.25rem;
    height: 4.625rem;
}

.construct-icon2 {
    width: 11.0625rem;
    height: 4.5rem;
}

.construct-icon3 {
    width: 10.875rem;
    height: 4.1875rem;
}

.construct-item-title {
    margin-top: 2.5rem;
    font-weight: bold;
    font-size: 20px;
}

.construct-item-desc {
    margin-top: 1.25rem;
    font-size: 14px;
}

.api-detail-information {
    margin-top: 6.875rem;
    padding-top: 6.25rem;
    width: 100%;
    height: 59.625rem;
    background: #023B33;
}

.information-tips,
.information-title,
.information-desc {
    width: 100%;
    text-align: center;
}

.information-tips {
    font-weight: bold;
    font-size: 28px;
    color: #FFD301;
}

.information-title {
    margin-top: 3.125rem;
    font-weight: bold;
    font-size: 36px;
    color: #FFFFFF;
}

.information-desc {
    margin-top: 2.25rem;
    font-size: 18px;
    color: #AEB2C0;
}

.information-content {
    margin-top: 3.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 1s ease, transform 1s ease;
}

.information-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.information-content .content-right {
    width: 57.5rem;
    height: 31rem;
    background: #10B981;
    border-radius: 1.625rem;
}

.content-left-item {
    display: flex;
    align-items: center;
    width: 15.375rem;
    height: 6.5rem;
    font-weight: 800;
    font-size: 24px;
    color: rgba(255, 255, 255, 0.7);
    border-left: 1px solid #345C57;
    cursor: pointer;
}

.content-left-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1.5rem;
    margin-left: 1.125rem;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.content-left-item-icon img {
    width: 2.3125rem;
    height: 2.3125rem;
}

.content-left-item.active {
    background: rgba(255, 255, 255, 0.2);
    border-left: 3px solid #43cd66;
}

.content-left-item.active .content-left-item-icon {
    background: #10B981;
}

.content-left-item.active {
    color: #fff;
}

.information-content .content-right-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
}

.content-right-item-title {
    font-size: 20px;
    color: #FFFFFF;
}

.content-right-item-icon1 {
    margin-top: 2.5rem;
    width: 28rem;
    height: 24.125rem;
}

.content-right-item-icon2 {
    margin-top: 1.5625rem;
    width: 28.625rem;
    height: 25.125rem;
}

.content-right-item-icon3 {
    margin-top: 2.5rem;
    width: 36.5rem;
    height: 24.125rem;
}

.content-right-item-icon4 {
    margin-top: 1.25rem;
    width: 29.875rem;
    height: 26rem;
}

.api-detail-interaction {
    margin-top: 6.875rem;
}

.interaction-tips,
.interaction-desc {
    width: 100%;
    text-align: center;
}

.interaction-tips {
    margin-bottom: 3.125rem;
    font-size: 28px;
    font-weight: bold;
    color: #10B981;
}

.interaction-desc {
    margin-top: 2.25rem;
    font-size: 18px;
}

.interaction-content .content-item {
    margin-top: 6.25rem;
    display: flex;
    align-items: center;
    justify-content: space-around;
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.interaction-content .content-item:nth-child(even) {
    transform: translateX(50px);
}

.interaction-content .content-item.animate {
    opacity: 1;
    transform: translateX(0);
}

.interaction-content .content-item .content-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25rem;
    height: 21.875rem;
    background: #EFF2FB;
    border-radius: 1.875rem;
}

.interaction-content .content-item .content-item-icon img {
    width: 22.8125rem;
    height: 19.6875rem;
}

.item-text-title {
    margin-bottom: 1.875rem;
    font-weight: bold;
    font-size: 34px;
}

.item-text-desc {
    display: flex;
    align-items: center;
    margin-top: 1.25rem;
}

.item-text-desc img {
    margin-right: 0.625rem;
    width: 1.125rem;
    height: 1.125rem;
}

.major-function-content {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 1s ease, transform 1s ease;
}

.major-function-content.animate {
    opacity: 1;
    transform: translateY(0);
}

.major-function-content-right-item {
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.major-function-content-right-item.animate {
    opacity: 1;
    transform: translateX(0);
}